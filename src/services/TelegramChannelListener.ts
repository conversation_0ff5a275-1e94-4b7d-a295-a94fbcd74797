import Logger from '../libs/Logger';
import { TelegramClient } from 'telegram';
import { StringSession } from 'telegram/sessions';
import { Api } from 'telegram';

type LoginHandlers = {
  phoneNumber: () => Promise<string>;
  phoneCode: () => Promise<string>;
  onError: (err: unknown) => void;
};

type MessageHistoryOptions = {
  channelUsername?: string;
  channelId?: string;
  limit?: number;
  offsetDate?: Date;
  offsetId?: number;
  maxId?: number;
  minId?: number;
  addOffset?: number;
  hash?: bigint;
};

type ChannelMessage = {
  id: number;
  message: string;
  date: Date;
  fromId?: string;
  views?: number;
  forwards?: number;
  replies?: number;
};

export class TelegramChannelListener {
  private client: TelegramClient;
  private session: StringSession;

  constructor(apiId: number, apiHash: string, sessionString = '') {
    this.session = new StringSession(sessionString);
    this.client = new TelegramClient(this.session, apiId, apiHash, {
      connectionRetries: 2,
    });
  }

  public async connect(config: LoginHandlers): Promise<void> {
    try {
      if (this.session.save()) {
        await this.client.connect();
      } else {
        await this.client.start({
          phoneNumber: config.phoneNumber,
          phoneCode: config.phoneCode,
          onError: config.onError,
        });
      }

      const me = await this.client.getMe();
      Logger.info('Connected as user:', me);
      Logger.info('Save this session string:');
      Logger.info(this.client.session.save());
    } catch (error) {
      Logger.error('Connection error:', error);
      throw error; // Re-throw to let caller handle the error
    }
  }

  public listenChannel(callback: (msg: string, update: any) => void): void {
    this.client.addEventHandler((update: any) => {
      if (update.className === 'UpdateNewChannelMessage') {
        Logger.info('Update from', update);
        const msg = update.message.message;
        callback(msg, update);
      }
    });
  }

  public async getChannelHistory(options: MessageHistoryOptions): Promise<ChannelMessage[]> {
    try {
      // Resolve channel entity
      let channel;
      if (options.channelUsername) {
        channel = await this.client.getEntity(options.channelUsername);
      } else if (options.channelId) {
        channel = await this.client.getEntity(options.channelId);
      } else {
        throw new Error('Either channelUsername or channelId must be provided');
      }

      const result = await this.client.invoke(
        new Api.messages.GetHistory({
          peer: channel,
          limit: options.limit || 100,
          offsetDate: options.offsetDate ? Math.floor(options.offsetDate.getTime() / 1000) : 0,
          offsetId: options.offsetId || 0,
          maxId: options.maxId || 0,
          minId: options.minId || 0,
          addOffset: options.addOffset || 0,
          hash: options.hash || (0 as any),
        }),
      );

      // Process and format messages
      const messages: ChannelMessage[] = [];

      if ('messages' in result) {
        for (const msg of result.messages) {
          if (msg.className === 'Message' && msg.message) {
            messages.push({
              id: msg.id,
              message: msg.message,
              date: new Date(msg.date * 1000),
              fromId: msg.fromId?.toString(),
              views: msg.views,
              forwards: msg.forwards,
              replies: msg.replies?.replies,
            });
          }
        }
      }

      Logger.info(`Retrieved ${messages.length} messages from channel`);
      return messages;
    } catch (error) {
      Logger.error('Error getting channel history:', error);
      throw error;
    }
  }

  public async getRecentMessages(channelUsername: string, limit: number = 50): Promise<ChannelMessage[]> {
    return this.getChannelHistory({
      channelUsername,
      limit,
    });
  }

  public async getMessagesByDateRange(
    channelUsername: string,
    fromDate: Date,
    toDate?: Date,
    limit: number = 200,
  ): Promise<ChannelMessage[]> {
    const messages = await this.getChannelHistory({
      channelUsername,
      limit,
      offsetDate: toDate,
    });

    return messages.filter((msg) => {
      const msgDate = msg.date;
      const isAfterFrom = msgDate >= fromDate;
      const isBeforeTo = toDate ? msgDate <= toDate : true;
      return isAfterFrom && isBeforeTo;
    });
  }
}
